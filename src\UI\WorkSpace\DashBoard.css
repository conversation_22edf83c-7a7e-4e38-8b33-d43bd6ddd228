table {
   width: 100%; 
}

td {
   border: solid 1px #f9fcff;
  vertical-align:top;
  /* border: solid 1px #eeeeee; */
}

.tdTreeExplorer {
  width: 17vw; 
  max-width: 17vw;   
}

.tdDiagramEditor {
  width: 75vw; 
  max-width: 75vw;  
}

.tdElements {
  width: 8vw; 
  max-width: 8vw;   
}


.link-project {
  color:black;   
}

.link-project:hover {
  color:black; 
  text-decoration: underline !important;  
}

.div-container-projects {
  height: 35vh;
  max-height: 35vh;
  overflow: auto;
}

.td-treexplorer{
width: 20rem;
max-width: 20rem;
min-width: 20rem;
}

.td-chatbot{
  width: 20rem;
  max-width: 20rem;
  min-width: 20rem;
}