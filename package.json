{"name": "variamos-ple", "homepage": "/", "jest": {"coverageThreshold": {"global": {"branches": 1, "functions": 1, "lines": 1, "statements": 1}}}, "version": "1.0.0", "private": true, "proxy": "http://localhost:12434", "dependencies": {"@variamosple/variamos-components": "^0.0.23", "alertifyjs": "^1.13.1", "axios": "^0.21.1", "bootstrap": "^5.3.3", "bootstrap-dark-5": "^1.1.3", "buffer": "^6.0.3", "fs": "^0.0.1-security", "gapi-script": "^1.2.0", "immer": "^9.0.12", "mxgraph": "^4.2.2", "os": "^0.1.2", "path": "^0.12.7", "prism-themes": "^1.9.0", "prismjs": "^1.29.0", "react": "^18.3.1", "react-bootstrap": "^2.10.1", "react-bootstrap-icons": "1.11", "react-dom": "^18.3.1", "react-dotenv": "^0.1.3", "react-icons": "5.4", "react-router-dom": "^6.7.0", "react-scripts": "^5.0.1", "react-simple-code-editor": "^0.13.1", "reactflow": "^11.11.4", "recharts": "^2.15.1", "resizable-panes-react": "^6.0.26", "stream-browserify": "^3.0.0", "typescript": "^4.2.3", "variamos-ple": "file:"}, "scripts": {"start": "react-scripts start", "dev": "set PORT=3002 && react-scripts start", "build": "CI=false && react-scripts build", "buildwindows": "set CI=false && react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "docker:ci": "docker build -t variamos:latest .", "docker:deploy": "docker run -d --name variamos -p 3000:3000 variamos:latest "}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@stryker-mutator/core": "^6.0.2", "@stryker-mutator/jest-runner": "^6.0.2", "@testing-library/cypress": "^8.0.2", "@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.2.0", "@typed-mxgraph/typed-mxgraph": "^1.0.0", "@types/node": "^22.13.4", "@types/prismjs": "^1.26.0", "@types/react": "^18.3.18", "@types/react-bootstrap": "^0.32.32", "@types/react-dom": "^18.3.5", "@types/reactstrap": "^8.7.1", "cypress": "^9.7.0"}}